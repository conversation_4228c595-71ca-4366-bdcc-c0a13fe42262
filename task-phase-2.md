### **Prompt for AI Code Agent: Phase 2 - Processing View & FFmpeg Setup**

**Project:** AI Video Clipper
**Phase:** 2 - UI State Management and Processing View
**Objective:** Transition the UI after a video is selected. The uploader will be replaced by a new view containing a video preview and a status panel. This phase also includes the initial technical step of loading the FFmpeg library in the browser.

**Starting Point:** The application currently has a functional dashboard with an uploader component as built in Phase 1.

---

### **Step 1: Install New Dependencies**

Install the necessary libraries for video processing and icons:

```bash
npm install @ffmpeg/ffmpeg @ffmpeg/util lucide-react
```

---

### **Step 2: File Structure**

Create and modify the following files. We are adding three new components and updating the main page and the uploader.

```
src
├── app
│   ├── layout.tsx
│   └── page.tsx  // Will be modified
└── components
    ├── Header.tsx
    ├── Uploader.tsx        // Will be modified
    ├── ProcessingView.tsx  // New Component
    ├── VideoPreview.tsx    // New Component
    └── StatusPanel.tsx     // New Component
```

---

### **Step 3: Refactor for State Management (`src/app/page.tsx`)**

The main page will now manage the application's state.

*   **Objective:** Control which view is displayed (`Uploader` vs. `ProcessingView`) based on whether a video has been selected.
*   **Requirements:**
    1.  **Convert to Client Component:** Add the `'use client'` directive at the top.
    2.  **State Management:** Introduce two state variables using `useState`:
        *   `appState`: A string that can be `'idle'`, `'loaded'`, or `'processing'`. Initialize it to `'idle'`.
        *   `videoFile`: Can be a `File` object or `null`. Initialize it to `null`.
    3.  **Conditional Rendering:**
        *   If `appState` is `'idle'`, render the `Uploader` component.
        *   If `appState` is `'loaded'` or `'processing'`, render the new `ProcessingView` component.
    4.  **Callback Function:** Create a function `handleFileSelect(file: File)`. This function will:
        *   Set `videoFile` to the selected `file`.
        *   Set `appState` to `'loaded'`.
    5.  **Prop Drilling:**
        *   Pass the `handleFileSelect` function as a prop to the `Uploader` component.
        *   Pass the `videoFile` and `appState` as props to the `ProcessingView` component.

---

### **Step 4: Component Implementation & Modification**

**1. Uploader Component (`src/components/Uploader.tsx`)**

*   **Objective:** Modify the existing uploader to notify the parent page when a file is selected.
*   **Requirements:**
    1.  **Accept Prop:** The component should accept a new prop: `onFileSelect: (file: File) => void`.
    2.  **Update Logic:** In the `handleFileSelect` function (the one internal to the uploader), instead of `console.log(file)`, call the `onFileSelect(file)` prop. This delegates state control back to the parent `page.tsx`.

**2. ProcessingView Component (`src/components/ProcessingView.tsx`)**

*   **Objective:** Create the main layout for the processing stage.
*   **Requirements:**
    1.  **Accept Props:** It should accept `videoFile: File | null` and `appState: string`.
    2.  **Layout:** Create a responsive two-column layout using a flexbox or CSS grid.
        *   On medium screens and larger (`md:`), it should be a side-by-side view.
        *   On small screens, it should stack vertically.
    3.  **Left Column:** This column will contain the `VideoPreview` component. Give it a flex-grow property so it takes up more space (e.g., `flex-grow` or `w-2/3`).
    4.  **Right Column:** This column will contain the `StatusPanel` component (e.g., `w-1/3`).
    5.  **Pass Props:** Pass the `videoFile` prop down to `VideoPreview`. Pass the `appState` prop down to `StatusPanel`.

**3. VideoPreview Component (`src/components/VideoPreview.tsx`)**

*   **Objective:** Display the selected video to the user.
*   **Requirements:**
    1.  **Client Component:** Must be a client component (`'use client'`).
    2.  **Accept Prop:** It should accept `videoFile: File | null`.
    3.  **State:** Use a `useState` and `useEffect` hook to create a local URL for the video file.
        ```typescript
        const [videoUrl, setVideoUrl] = useState<string>('');

        useEffect(() => {
          if (videoFile) {
            const url = URL.createObjectURL(videoFile);
            setVideoUrl(url);
            // Cleanup function to revoke the object URL
            return () => URL.revokeObjectURL(url);
          }
        }, [videoFile]);
        ```
    4.  **Render Logic:** Render an HTML5 `<video>` element.
        *   Set its `src` to the `videoUrl` from the state.
        *   Include the `controls` attribute.
        *   Make it responsive with Tailwind classes (`w-full rounded-md`).

**4. StatusPanel Component (`src/components/StatusPanel.tsx`)**

*   **Objective:** The control panel for the user. It will show the steps, progress, and contain the trigger button.
*   **Requirements:**
    1.  **Client Component:** Must be a client component (`'use client'`).
    2.  **Accept Prop:** It should accept `appState: string`.
    3.  **FFmpeg State & Logic:**
        *   Create a state variable `ffmpegLoaded`, initialized to `false`.
        *   Use a `useRef` to hold the FFmpeg instance: `const ffmpegRef = useRef(new FFmpeg());`.
        *   Create an async function `loadFfmpeg`. Inside, it will:
            *   Get the FFmpeg instance from the ref.
            *   Set a progress handler: `ffmpeg.on('log', ...)` to log messages for now.
            *   Load the library: `await ffmpeg.load(...)`.
            *   Set `ffmpegLoaded` to `true` upon completion.
        *   Use a `useEffect` hook to call `loadFfmpeg()` when the component mounts.
    4.  **UI Structure:**
        *   Use a `Card` component from `shadcn/ui` as the container.
        *   **Title:** An `<h2>` that says "Status".
        *   **Steps List:** Create an ordered or unordered list (`<ul>`) of the processing steps. For now, these are just static UI elements.
            *   `<li>Initialize AI Engine</li>`
            *   `<li>Generate Transcript</li>`
            *   `<li>Analyze Key Moments</li>`
            *   `<li>Cut Video Clips</li>`
        *   **Visual Indicators:** Next to each list item, use an icon from `lucide-react`.
            *   Use `<LoaderCircle>` icon for the "Initialize AI Engine" step. Its animation should be active if `!ffmpegLoaded`. When `ffmpegLoaded` is `true`, change it to a `<CheckCircle>` icon.
            *   For all other steps, use a `<CircleDashed>` icon for now.
        *   **Action Button:**
            *   At the bottom, add a `Button` component from `shadcn/ui`.
            *   The button text should be "Start Clipping".
            *   The button's `disabled` attribute should be set to `!ffmpegLoaded` or `appState !== 'loaded'`. This prevents the user from starting the process before FFmpeg is ready. For now, its `onClick` can be an empty function.

---

### **Final Deliverable**

Please provide the complete, copy-and-paste-ready code for the following files:
1.  `src/app/page.tsx` (Modified)
2.  `src/components/Uploader.tsx` (Modified)
3.  `src/components/ProcessingView.tsx` (New)
4.  `src/components/VideoPreview.tsx` (New)
5.  `src/components/StatusPanel.tsx` (New)
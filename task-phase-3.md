
### **Prompt for AI Code Agent: Phase 3 - End-to-End Processing Logic**

**Project:** AI Video Clipper
**Phase:** 3 - The Core Processing Workflow
**Objective:** Wire up the "Start Clipping" button to execute the full, end-to-end process: extracting audio, transcribing, analyzing with AI to find timestamps, cutting the video in the browser, and displaying the final results.

**Starting Point:** The application has a processing view that loads FFmpeg and waits for user action.

---

### **Step 1: Environment and Dependencies**

1.  **Install new libraries:**
    ```bash
    npm install axios @google/generative-ai
    ```

2.  **Create Environment File:** In the root of your project, create a new file named `.env.local`. **This is crucial for security.** Add your secret API keys to this file.

    ```.env.local
    # Get from https://www.assemblyai.com/
    ASSEMBLYAI_API_KEY="YOUR_ASSEMBLYAI_API_KEY"

    # Get from https://makersuite.google.com/
    GEMINI_API_KEY="YOUR_GEMINI_API_KEY"
    ```

---

### **Step 2: Create Backend API Routes**

Create two new API routes inside `src/app/api/`. These will act as secure proxies for your third-party services.

**1. Transcription Route (`src/app/api/transcribe/route.ts`)**

*   **Objective:** Receives an audio file, sends it to AssemblyAI for transcription, and returns the transcript with word-level timestamps.
*   **Requirements:**
    *   Use the `POST` method.
    *   Read the audio file from the incoming `FormData`.
    *   Use `axios` to interact with the AssemblyAI API. The process is two steps:
        1.  Upload the audio file to `/v2/upload` to get an `upload_url`.
        2.  Submit this `upload_url` to `/v2/transcript` to start the transcription job. Set `word_boost` and `speaker_labels` if desired, and ensure `auto_highlights` is true for potential future use.
    *   Implement polling: After submitting the job, repeatedly check the job status endpoint (`/v2/transcript/JOB_ID`) until it is `completed` or `error`.
    *   Once completed, return the full transcript object as JSON.
    *   Include robust error handling.

**2. Analysis Route (`src/app/api/analyze/route.ts`)**

*   **Objective:** Receives transcript text, sends it to the Gemini API, and returns a structured list of clip-worthy timestamps.
*   **Requirements:**
    *   Use the `POST` method.
    *   Initialize the Google Generative AI client using your `GEMINI_API_KEY`.
    *   Use the `gemini-pro` model.
    *   **Crucially, construct a very specific prompt.** The prompt should instruct the AI to act as a viral video editor, identify 3-5 key moments, and return **only a JSON array** of objects with `start` and `end` keys in seconds. Provide an example in the prompt.
    *   After getting the response from Gemini, parse the text to extract the JSON.
    *   Return the parsed array of timestamps.
    *   Include robust error handling.

---

### **Step 3: Update Frontend State Management**

**1. Main Page (`src/app/page.tsx`)**

*   **Objective:** Enhance the page's state to manage the processing steps and the final results.
*   **Requirements:**
    1.  Add two new state variables:
        ```typescript
        const [processingStep, setProcessingStep] = useState<string>('idle');
        const [resultClips, setResultClips] = useState<any[]>([]);
        ```
    2.  Create a new callback function `handleStartProcessing()`. This function will contain the main orchestration logic and will be passed down to the `StatusPanel`.
    3.  Create a `handleReset()` function that resets all state variables back to their initial values (`appState: 'idle'`, `videoFile: null`, etc.). This will allow the user to start over.
    4.  Update the props passed to `ProcessingView` to include these new states and callbacks.

---

### **Step 4: Major Component Updates**

**1. StatusPanel Component (`src/components/StatusPanel.tsx`)**

*   **Objective:** Make this component the "brain" of the frontend processing. It will call the APIs and FFmpeg in sequence and update the UI accordingly.
*   **Requirements:**
    1.  **Accept New Props:** `processingStep: string` and `onStart: () => Promise<void>`.
    2.  **Dynamic UI:** The list of steps should now dynamically change its icons and styles based on the `processingStep` prop.
        *   `idle`: All icons are `<CircleDashed>`.
        *   `transcribing`: The "Generate Transcript" step gets a `<LoaderCircle>`, the one before it gets a `<CheckCircle>`.
        *   ... and so on for `analyzing`, `clipping`.
        *   `done`: All icons are `<CheckCircle>`.
    3.  **Update Button Logic:** The "Start Clipping" button's `onClick` should now call the `onStart` prop.

**2. ProcessingView Component (`src/components/ProcessingView.tsx`)**

*   **Objective:** Conditionally render the results view when the process is complete.
*   **Requirements:**
    1.  Add a new component import: `ResultsView`.
    2.  Update the rendering logic:
        *   If `processingStep` is `'done'`, render the `ResultsView` component.
        *   Otherwise, render the existing two-column layout with `VideoPreview` and `StatusPanel`.
    3.  Pass the `resultClips` and `onReset` function as props to `ResultsView`.

---

### **Step 5: Orchestration Logic (`page.tsx`)**

Implement the `handleStartProcessing` function inside `src/app/page.tsx`. This is the most critical part.

*   **Objective:** Define the step-by-step browser-side workflow.
*   **Logic Flow:**
    1.  Set `appState` to `'processing'`.
    2.  **Extract Audio:**
        *   Set `processingStep` to `'extractingAudio'`.
        *   Use `ffmpeg.wasm` to convert the `videoFile` into an in-memory MP3 audio blob.
    3.  **Transcribe:**
        *   Set `processingStep` to `'transcribing'`.
        *   Create `FormData` and append the audio blob.
        *   `POST` this data to your `/api/transcribe` endpoint. Await the JSON response.
    4.  **Analyze:**
        *   Set `processingStep` to `'analyzing'`.
        *   `POST` the transcript text from the previous step to `/api/analyze`. Await the timestamp array.
    5.  **Clip Videos:**
        *   Set `processingStep` to `'clipping'`.
        *   Loop through the timestamp array. For each object:
            *   Execute an `ffmpeg.wasm` command to cut the original `videoFile` using the `start` and `end` times. Use the `-c copy` flag for speed.
            *   The output will be an in-memory video blob.
            *   Create a local object URL for the blob using `URL.createObjectURL()`.
            *   Store this URL and the transcript for that segment in an array.
    6.  **Finish:**
        *   Set `setResultClips` with the array of generated clip URLs.
        *   Set `processingStep` to `'done'`.

---

### **Step 6: New Component for Results (`src/components/ResultsView.tsx`)**

*   **Objective:** Display the final generated clips for preview and download.
*   **Requirements:**
    1.  **Accept Props:** `clips: any[]` and `onReset: () => void`.
    2.  **Layout:**
        *   Display a title like "Your Clips Are Ready!".
        *   Create a grid layout to display multiple "Clip Cards".
        *   Render a `Button` with "Process Another Video" that calls the `onReset` prop.
    3.  **Clip Card Logic:**
        *   Map over the `clips` prop.
        *   For each clip, render a `Card` component.
        *   Inside the card:
            *   A `<video>` element with its `src` set to the clip's object URL.
            *   A `<p>` tag displaying the transcript for that segment.
            *   A "Download" `Button`. The `onClick` for this should create a temporary `<a>` tag with a `download` attribute and the blob URL to trigger a file download.

---

### **Final Deliverable**

Please provide the complete, copy-and-paste-ready code for the following files:
1.  `.env.local` (Template)
2.  `src/app/api/transcribe/route.ts` (New)
3.  `src/app/api/analyze/route.ts` (New)
4.  `src/app/page.tsx` (Heavily Modified)
5.  `src/components/StatusPanel.tsx` (Modified)
6.  `src/components/ProcessingView.tsx` (Modified)
7.  `src/components/ResultsView.tsx` (New)
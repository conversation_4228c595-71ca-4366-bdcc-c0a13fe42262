import { NextRequest, NextResponse } from 'next/server';
import { writeFile } from 'fs/promises';
import { join } from 'path';

export async function POST(request: NextRequest) {
  try {
    console.log('🐛 Debug audio API called');
    
    // Get the audio file from FormData
    const formData = await request.formData();
    const audioFile = formData.get('audio') as File;

    if (!audioFile) {
      console.error('❌ No audio file provided');
      return NextResponse.json(
        { error: 'No audio file provided' },
        { status: 400 }
      );
    }

    console.log('📁 Audio file received:', {
      name: audioFile.name,
      size: audioFile.size,
      type: audioFile.type,
      lastModified: audioFile.lastModified
    });

    // Convert file to buffer
    const audioBuffer = await audioFile.arrayBuffer();
    console.log('🔄 Audio buffer size:', audioBuffer.byteLength);

    // Save the audio file to the public directory for inspection
    const fileName = `debug_audio_${Date.now()}.mp3`;
    const filePath = join(process.cwd(), 'public', fileName);
    
    await writeFile(filePath, Buffer.from(audioBuffer));
    console.log('💾 Audio file saved to:', filePath);

    return NextResponse.json({
      success: true,
      fileName,
      fileSize: audioBuffer.byteLength,
      downloadUrl: `/${fileName}`,
      message: 'Audio file saved successfully for debugging'
    });

  } catch (error) {
    console.error('❌ Debug audio API error:', error);
    return NextResponse.json(
      { error: 'Failed to process audio file', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

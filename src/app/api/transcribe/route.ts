import { NextRequest, NextResponse } from 'next/server';
import axios from 'axios';

const ASSEMBLYAI_API_KEY = process.env.ASSEMBLYAI_API_KEY;
const ASSEMBLYAI_BASE_URL = 'https://api.assemblyai.com/v2';

export async function POST(request: NextRequest) {
  try {
    console.log('🎵 Transcription API called');

    if (!ASSEMBLYAI_API_KEY) {
      console.error('❌ AssemblyAI API key not configured');
      return NextResponse.json(
        { error: 'AssemblyAI API key not configured' },
        { status: 500 }
      );
    }

    // Get the audio file from FormData
    const formData = await request.formData();
    const audioFile = formData.get('audio') as File;

    if (!audioFile) {
      console.error('❌ No audio file provided');
      return NextResponse.json(
        { error: 'No audio file provided' },
        { status: 400 }
      );
    }

    console.log('📁 Audio file received:', {
      name: audioFile.name,
      size: audioFile.size,
      type: audioFile.type,
      lastModified: audioFile.lastModified
    });

    // Convert file to buffer
    const audioBuffer = await audioFile.arrayBuffer();
    console.log('🔄 Audio buffer size:', audioBuffer.byteLength);

    // Step 1: Upload the audio file to AssemblyAI
    console.log('⬆️ Uploading audio to AssemblyAI...');
    const uploadResponse = await axios.post(
      `${ASSEMBLYAI_BASE_URL}/upload`,
      audioBuffer,
      {
        headers: {
          'Authorization': ASSEMBLYAI_API_KEY,
          'Content-Type': 'application/octet-stream',
        },
      }
    );

    const uploadUrl = uploadResponse.data.upload_url;
    console.log('✅ Upload successful, URL:', uploadUrl);

    // Step 2: Submit transcription job
    console.log('🚀 Starting transcription job...');
    const transcriptResponse = await axios.post(
      `${ASSEMBLYAI_BASE_URL}/transcript`,
      {
        audio_url: uploadUrl,
        // word_boost: ['video', 'content', 'moment', 'highlight'],
        speaker_labels: true,
        format_text: true,
        punctuate: true,
        speech_model: "universal",
        language_detection: true,
      },
      {
        headers: {
          'Authorization': ASSEMBLYAI_API_KEY,
          'Content-Type': 'application/json',
        },
      }
    );

    const transcriptId = transcriptResponse.data.id;
    console.log('📝 Transcription job created, ID:', transcriptId);

    // Step 3: Poll for completion
    console.log('⏳ Polling for transcription completion...');
    let transcript;
    let attempts = 0;
    const maxAttempts = 60; // 5 minutes max (5 second intervals)

    while (attempts < maxAttempts) {
      const statusResponse = await axios.get(
        `${ASSEMBLYAI_BASE_URL}/transcript/${transcriptId}`,
        {
          headers: {
            'Authorization': ASSEMBLYAI_API_KEY,
          },
        }
      );

      transcript = statusResponse.data;
      console.log(`🔄 Poll attempt ${attempts + 1}, status: ${transcript.status}`);

      if (transcript.status === 'completed') {
        console.log('✅ Transcription completed!');
        console.log('📊 Transcript stats:', {
          textLength: transcript.text?.length || 0,
          wordsCount: transcript.words?.length || 0,
          confidence: transcript.confidence,
          audio_duration: transcript.audio_duration
        });
        break;
      } else if (transcript.status === 'error') {
        console.error('❌ Transcription error:', transcript.error);
        throw new Error(`Transcription failed: ${transcript.error}`);
      }

      // Wait 5 seconds before next poll
      await new Promise(resolve => setTimeout(resolve, 5000));
      attempts++;
    }

    if (attempts >= maxAttempts) {
      console.error('❌ Transcription timeout after', maxAttempts, 'attempts');
      throw new Error('Transcription timeout');
    }

    console.log('🎉 Returning transcript data');

    // Save transcript to file for debugging
    try {
      const fs = await import('fs/promises');
      const path = await import('path');
      const debugFile = path.join(process.cwd(), 'public', `debug_transcript_${Date.now()}.json`);
      await fs.writeFile(debugFile, JSON.stringify(transcript, null, 2));
      console.log('💾 Transcript saved for debugging:', debugFile);
    } catch (debugError) {
      console.warn('⚠️ Could not save debug transcript:', debugError);
    }

    return NextResponse.json(transcript);

  } catch (error) {
    console.error('❌ Transcription API error:', error);
    if (error instanceof Error) {
      console.error('Error message:', error.message);
      console.error('Error stack:', error.stack);
    }
    return NextResponse.json(
      { error: 'Failed to transcribe audio', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";

const inter = Inter({
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "AI Video Clipper",
  description: "Upload a long-form video, and our AI will find and clip the most engaging moments for you.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${inter.className} bg-slate-900 text-slate-50 min-h-screen flex flex-col items-center antialiased`}
      >
        {children}
      </body>
    </html>
  );
}

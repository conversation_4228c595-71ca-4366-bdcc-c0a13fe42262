'use client';

import { useState, useEffect } from 'react';

interface VideoPreviewProps {
  videoFile: File | null;
}

export default function VideoPreview({ videoFile }: VideoPreviewProps) {
  const [videoUrl, setVideoUrl] = useState<string>('');

  useEffect(() => {
    if (videoFile) {
      const url = URL.createObjectURL(videoFile);
      setVideoUrl(url);
      // Cleanup function to revoke the object URL
      return () => URL.revokeObjectURL(url);
    }
  }, [videoFile]);

  if (!videoFile) {
    return (
      <div className="w-full h-64 bg-slate-800 rounded-md flex items-center justify-center">
        <p className="text-slate-400">No video selected</p>
      </div>
    );
  }

  return (
    <div className="w-full">
      <video
        src={videoUrl}
        controls
        className="w-full rounded-md"
      >
        Your browser does not support the video tag.
      </video>
    </div>
  );
}

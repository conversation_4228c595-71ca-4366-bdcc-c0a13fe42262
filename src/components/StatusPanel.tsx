'use client';

import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { LoaderCircle, CheckCircle, CircleDashed } from 'lucide-react';

interface StatusPanelProps {
  appState: string;
  processingStep: string;
  onStart: () => Promise<void>;
  onDebugAudio?: () => Promise<void>;
  progress: number;
  ffmpegLoaded: boolean;
}

export default function StatusPanel({ appState, processingStep, onStart, onDebugAudio, progress, ffmpegLoaded }: StatusPanelProps) {

  const getStepIcon = (stepName: string) => {
    const currentSteps = ['idle', 'extractingAudio', 'transcribing', 'analyzing', 'clipping', 'done'];
    const stepIndex = currentSteps.indexOf(processingStep);

    switch (stepName) {
      case 'initialize':
        return ffmpegLoaded ? (
          <CheckCircle className="w-5 h-5 text-green-500" />
        ) : (
          <LoaderCircle className="w-5 h-5 text-blue-500 animate-spin" />
        );
      case 'transcribe':
        if (stepIndex >= 2 && stepIndex < 3) {
          return <LoaderCircle className="w-5 h-5 text-blue-500 animate-spin" />;
        } else if (stepIndex >= 3) {
          return <CheckCircle className="w-5 h-5 text-green-500" />;
        }
        return <CircleDashed className="w-5 h-5 text-slate-400" />;
      case 'analyze':
        if (stepIndex >= 3 && stepIndex < 4) {
          return <LoaderCircle className="w-5 h-5 text-blue-500 animate-spin" />;
        } else if (stepIndex >= 4) {
          return <CheckCircle className="w-5 h-5 text-green-500" />;
        }
        return <CircleDashed className="w-5 h-5 text-slate-400" />;
      case 'clip':
        if (stepIndex >= 4 && stepIndex < 5) {
          return <LoaderCircle className="w-5 h-5 text-blue-500 animate-spin" />;
        } else if (stepIndex >= 5) {
          return <CheckCircle className="w-5 h-5 text-green-500" />;
        }
        return <CircleDashed className="w-5 h-5 text-slate-400" />;
      default:
        return <CircleDashed className="w-5 h-5 text-slate-400" />;
    }
  };

  return (
    <Card className="p-6">
      <h2 className="text-xl font-semibold mb-4">Status</h2>

      <ul className="space-y-4 mb-6">
        <li className="space-y-2">
          <div className="flex items-center gap-3">
            {getStepIcon('initialize')}
            <span>Initialize AI Engine</span>
          </div>
          {processingStep === 'extractingAudio' && (
            <div className="ml-8">
              <Progress value={progress} className="h-2" />
              <p className="text-sm text-slate-600 mt-1">Extracting audio from video...</p>
            </div>
          )}
        </li>

        <li className="space-y-2">
          <div className="flex items-center gap-3">
            {getStepIcon('transcribe')}
            <span>Generate Transcript</span>
          </div>
          {processingStep === 'transcribing' && (
            <div className="ml-8">
              <div className="flex items-center space-x-2">
                <LoaderCircle className="w-4 h-4 animate-spin" />
                <p className="text-sm text-slate-600">This can take a few minutes for longer videos...</p>
              </div>
            </div>
          )}
        </li>

        <li className="space-y-2">
          <div className="flex items-center gap-3">
            {getStepIcon('analyze')}
            <span>Analyze Key Moments</span>
          </div>
          {processingStep === 'analyzing' && (
            <div className="ml-8">
              <div className="flex items-center space-x-2">
                <LoaderCircle className="w-4 h-4 animate-spin" />
                <p className="text-sm text-slate-600">AI is identifying the best moments...</p>
              </div>
            </div>
          )}
        </li>

        <li className="space-y-2">
          <div className="flex items-center gap-3">
            {getStepIcon('clip')}
            <span>Cut Video Clips</span>
          </div>
          {processingStep === 'clipping' && (
            <div className="ml-8">
              <Progress value={progress} className="h-2" />
              <p className="text-sm text-slate-600 mt-1">Creating video clips...</p>
            </div>
          )}
        </li>
      </ul>

      <div className="space-y-2">
        <Button
          onClick={onStart}
          disabled={appState === 'processing' || !ffmpegLoaded || appState !== 'loaded'}
          className="w-full"
        >
          {appState === 'processing' && <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />}
          {!ffmpegLoaded && appState !== 'processing' && <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />}
          {appState === 'processing' ? 'Processing...' : !ffmpegLoaded ? 'Loading Engine...' : 'Start Clipping'}
        </Button>

        {onDebugAudio && (
          <Button
            onClick={onDebugAudio}
            disabled={!ffmpegLoaded || appState !== 'loaded'}
            variant="outline"
            className="w-full"
          >
            🐛 Debug Audio Extraction
          </Button>
        )}
      </div>
    </Card>
  );
}

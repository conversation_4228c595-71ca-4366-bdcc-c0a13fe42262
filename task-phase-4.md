
### **Prompt for AI Code Agent: Phase 4 - UX Refinement, Subtitles, and Error Handling**

**Project:** AI Video Clipper
**Phase:** 4 - User Experience and Production Polish
**Objective:** Enhance the application by providing real-time progress feedback, adding the ability to download subtitles, implementing comprehensive error handling, and adding general UI polish.

**Starting Point:** The application can successfully process a video from start to finish, but it lacks detailed feedback and resilience.

---

### **Step 1: Add Progress Bar Component**

1.  Add the `Progress` component from `shadcn/ui` to your project.
    ```bash
    npx shadcn-ui@latest add progress
    ```

---

### **Step 2: Implement Real-Time Progress Reporting**

**Objective:** Show the user the progress of time-consuming FFmpeg tasks (audio extraction and clipping).

**1. Update State in `src/app/page.tsx`**

*   Add a new state variable to track progress percentage:
    ```typescript
    const [progress, setProgress] = useState<number>(0);
    ```
*   Pass `progress` and the `setProgress` function as props down to `ProcessingView` and then to `StatusPanel`.

**2. Modify the Orchestration Logic in `src/app/page.tsx`**

*   In the `handleStartProcessing` function, you will now attach progress listeners to your FFmpeg commands.
*   First, create a reference to your FFmpeg instance at the top of the `page.tsx` component so it's accessible within `handleStartProcessing`. You will need to pass this ref down from `StatusPanel` or hoist the FFmpeg state up to `page.tsx`. A simpler approach is to move the FFmpeg instance management to `page.tsx`.
*   **Refactor:** Move the `ffmpegRef` and `loadFfmpeg` logic from `StatusPanel` up to `page.tsx`. The page will now be responsible for loading FFmpeg. Pass the `ffmpegLoaded` state down.
*   Update the FFmpeg execution calls:
    ```typescript
    const ffmpeg = ffmpegRef.current;
    ffmpeg.on('progress', ({ progress, time }) => {
      setProgress(Math.round(progress * 100));
    });

    // Example for extracting audio
    await ffmpeg.exec(['-i', 'input.mp4', '-vn', '-c:a', 'mp3', 'audio.mp3']);

    // Example for clipping
    await ffmpeg.exec(['-i', 'input.mp4', '-ss', '...']);

    // Important: After each command, reset progress for the next one
    setProgress(0);
    ```

**3. Update `src/components/StatusPanel.tsx`**

*   Accept the `progress` prop (a number from 0 to 100).
*   Conditionally render the `Progress` component from `shadcn/ui` next to the currently active step.
*   For example, when `processingStep` is `'extractingAudio'`, display the progress bar under that list item. The `value` of the progress bar should be the `progress` prop.
*   The progress bar should only be visible for the active step.

---

### **Step 3: Add Subtitle (SRT) Generation**

**Objective:** Allow users to download a standard `.srt` subtitle file for each generated clip.

**1. Create a Utility Function (`src/lib/srt.ts`)**

*   Create a new file `src/lib/srt.ts`.
*   This file will export a function `formatToSRT`. This function takes the `words` array from the AssemblyAI response and formats it into the SRT format.
    ```typescript
    // src/lib/srt.ts

    interface Word {
      text: string;
      start: number;
      end: number;
    }

    const formatTime = (milliseconds: number) => {
      // Converts milliseconds (e.g., 5500) to SRT time format (00:00:05,500)
      // ... implementation details
    };

    export const generateSRT = (words: Word[]): string => {
      let srtContent = '';
      let segment = '';
      let startTime = 0;
      let segmentIndex = 1;
      const maxSegmentLength = 32; // Characters per line

      // Logic to group words into reasonably-sized subtitle lines
      // and format them with index, timestamps, and text.
      // ... implementation details
      
      return srtContent;
    };
    ```

**2. Update Orchestration Logic in `src/app/page.tsx`**

*   During the "Clipping" loop, in addition to creating a video blob, you also need to filter the `words` from the full transcript that fall within the clip's `start` and `end` time.
*   The `resultClips` state should now hold an array of objects, each containing the `url` and the filtered `words` array for that clip:
    ```typescript
    // Example object in the resultClips array
    {
      url: 'blob:http://...',
      words: [ { text: 'Hello', start: 65200, end: 65400 }, ... ]
    }
    ```

**3. Update `src/components/ResultsView.tsx`**

*   In each "Clip Card", add a new button: "Download .SRT".
*   Create a `handleDownloadSRT` function within the component.
*   When the button is clicked, this function will:
    1.  Call the `generateSRT` utility with the clip's specific `words` array.
    2.  Create a new `Blob` from the returned SRT string with `type: 'text/plain'`.
    3.  Create an object URL for the blob.
    4.  Create a temporary `<a>` element, set its `href` and `download` attributes (e.g., `download="clip_1_subtitles.srt"`), and programmatically click it to trigger the download.

---

### **Step 4: Implement Comprehensive Error Handling**

**Objective:** Gracefully handle failures during the process and inform the user.

**1. Update State in `src/app/page.tsx`**

*   Add a new state for error messages:
    ```typescript
    const [errorMessage, setErrorMessage] = useState<string | null>(null);
    ```
*   Pass `errorMessage` down to the relevant components.

**2. Update `handleStartProcessing` in `src/app/page.tsx`**

*   Wrap the entire contents of the function in a `try...catch (error)` block.
*   Inside the `catch` block:
    *   `console.error("Processing failed:", error);`
    *   Set `setErrorMessage("An unexpected error occurred. Please check the console or try a different file.");`
    *   Set `setAppState('error');`

**3. Update `src/components/ProcessingView.tsx`**

*   Add a new conditional rendering block. If `appState` is `'error'`, render an "Error View".
*   This Error View should display:
    *   An alert or card component.
    *   An error icon (e.g., from `lucide-react`).
    *   The `errorMessage` string.
    *   The "Process Another Video" (`onReset`) button.

---

### **Step 5: General UI Polish**

**1. Loading State on Button (`src/components/StatusPanel.tsx`)**

*   When the "Start Clipping" button is clicked, it should enter a loading state.
*   Modify the button to show a `LoaderCircle` icon and disable it while `appState` is `'processing'`.
    ```jsx
    <Button disabled={appState === 'processing' || !ffmpegLoaded}>
      {appState === 'processing' && <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />}
      Start Clipping
    </Button>
    ```

**2. More Descriptive Status Text (`src/components/StatusPanel.tsx`)**

*   Instead of just a spinner, add sub-text to the list items to give the user more context. For example, under "Generate Transcript", you could add a small `<p>` that says "This can take a few minutes for longer videos."

---

### **Final Deliverable**

Please provide the complete, copy-and-paste-ready code for the following files:
1.  `src/app/page.tsx` (Heavily Modified)
2.  `src/components/StatusPanel.tsx` (Modified)
3.  `src/components/ResultsView.tsx` (Modified)
4.  `src/lib/srt.ts` (New Utility File)
5.  `src/components/ProcessingView.tsx` (Modified to include error view)
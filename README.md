## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [<PERSON><PERSON><PERSON>](https://vercel.com/font), a new font family for Vercel.

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.


### Core Concept: The "Browser as a Factory"

The entire process will happen on the user's machine, within their browser tab. The Next.js application is simply the "user interface" and the "orchestrator" that tells the browser what to do. The server's only job is to deliver the web application and, in a limited capacity, securely handle API keys.

**Philosophy:** No databases, no user accounts, no saved state. A user visits the site, performs a task, and leaves with the results. It's a utility, not a service.

---

### Technology Stack (Simplified)

1.  **Framework:** Next.js with the App Router.
2.  **UI:** Tailwind CSS for styling and Shadcn/UI for pre-built components (like buttons, progress bars) to get a polished look quickly.
3.  **Video Processing:** **`ffmpeg.wasm`**. This is the core engine for all video manipulation.
4.  **Transcription:** **AssemblyAI** or a similar service. We'll use a Next.js API Route as a secure proxy to make this call.
5.  **AI Analysis:** **Google Gemini API**. Also called via a secure Next.js API Route.
6.  **State Management:** React's built-in `useState` and `useContext` will be more than enough. No need for complex libraries.

---

### The User Journey & Detailed Process Flow

This is the step-by-step experience from the user's perspective.

#### **Phase 1: The Dashboard (The Welcome Screen)**

The user lands on the homepage. There is no login. The screen is clean and focused on a single action.

*   **UI Components:**
    *   **Headline:** "AI Video Clipper" or something similar.
    *   **Sub-headline:** "Upload a long-form video, and AI will find the best short clips for you."
    *   **The Uploader:** A large, central drag-and-drop area that says "Drag & Drop Your Video File Here" or a prominent "Select Video" button.
    *   **Constraints Text:** Small text below the uploader: "For personal use. All processing happens in your browser. Best for files under 500MB." (This manages user expectations).

#### **Phase 2: Upload & Configuration**

The user selects a video. The application state changes to the "Processing" view.

1.  **User Action:** The user drags a video file (e.g., `podcast_interview.mp4`) onto the dropzone.
2.  **Immediate UI Change:**
    *   The uploader component is replaced by a preview of the video (using an HTML `<video>` tag).
    *   A status panel appears on the side. This panel is the central hub for the entire process.

#### **Phase 3: The Clipping Process (The Magic)**

This is the core of the application. The user sees a checklist-style progress indicator in the status panel. Here’s what happens under the hood for each step:

**Step 1: Preparing Environment**
*   **What the User Sees:** Status panel shows a spinner: `[Loading...] Initializing AI Engine...`
*   **What's Happening (Code):**
    *   The `ffmpeg.wasm` library and its core files are loaded into the browser in the background. This may take a few seconds on the first visit. The UI shows a loading state until FFmpeg is ready.

**Step 2: Transcribing Audio**
*   **What the User Sees:** Status panel updates: `[In Progress...] Step 1 of 4: Generating Transcript... (This may take a few minutes)`
*   **What's Happening (Code):**
    1.  Using `ffmpeg.wasm`, you run a command to extract *only the audio* from the video file into an in-memory format (e.g., a Blob). `ffmpeg -i video.mp4 -vn -c:a mp3 audio.mp3`
    2.  This audio blob is sent from the browser to your **Next.js API Route** (`/api/transcribe`).
    3.  This server-side API route securely calls the **AssemblyAI** API with the audio data. *Why an API route? To hide your secret AssemblyAI API key from the user's browser.*
    4.  The API route streams the transcript (with word-level timestamps) back to the browser as it's being generated.

**Step 3: Analyzing Transcript**
*   **What the User Sees:** Status panel updates: `[In Progress...] Step 2 of 4: Finding Key Moments...`
*   **What's Happening (Code):**
    1.  The full transcript text is sent to another **Next.js API Route** (`/api/analyze`).
    2.  This route calls the **Gemini API** with a carefully crafted prompt, instructing it to find the best moments and return only a JSON array of start and end timestamps.
    3.  The array of timestamps is sent back to the browser.

**Step 4: Clipping Video**
*   **What the User Sees:** Status panel updates: `[In Progress...] Step 3 of 4: Cutting Video Clips (1 of 5)...` with a progress bar.
*   **What's Happening (Code):**
    1.  The browser now has the original video file and a list of timestamps (e.g., `[{start: 65.2, end: 93.8}, ...]`).
    2.  You loop through this list. For each timestamp pair, you execute a `ffmpeg.wasm` command in the browser:
        `ffmpeg -i original_video.mp4 -ss 65.2 -to 93.8 -c copy clip_1.mp4`
    3.  The `-c copy` flag is crucial. It tells FFmpeg to not re-encode the video, making the process incredibly fast.
    4.  Each output clip is stored in the browser's memory as a Blob.

**Step 5: Generating Subtitles (Optional but cool)**
*   **What the User Sees:** Status panel updates: `[In Progress...] Step 4 of 4: Adding Captions...`
*   **What's Happening (Code):**
    1.  For each generated clip, you can use the transcript data and `ffmpeg.wasm`'s subtitle capabilities (`-vf subtitles=...`) to burn the captions directly onto the video. This is an advanced step, but very powerful.

**Step 6: Finished!**
*   **What the User Sees:** The status panel shows a big checkmark: `[Complete!] Your clips are ready.` The main area of the page now populates with the results.

#### **Phase 4: Review and Download**

The processing is done. The user can now interact with the results.

*   **UI Components:**
    *   A grid or list of "Clip Cards" is displayed.
    *   **Each Card Contains:**
        *   A video player to preview the short clip.
        *   The transcribed text from that specific clip shown below the video.
        *   A "Download" button.
        *   A "Copy Subtitles (.srt)" button.
    *   **User Action:**
        *   The user can play each clip to see if they like it.
        *   Clicking "Download" will trigger a browser download for that specific MP4 file. There could also be a "Download All (.zip)" button.

That's the entire flow. The user never leaves the page, and all the heavy work is done on their machine. Once they close the tab, everything is gone, fulfilling the "simple utility" requirement.
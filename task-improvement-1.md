### The Dilemma: Text vs. Words

1.  **Sending only `"text"`:**
    *   **Pro:** Very few tokens, cheap and fast.
    *   **Con (Fatal Flaw):** As you said, the LLM has **zero context of time**. It can tell you *what* the best moment is ("the part about making chocolate"), but it has no way to provide the `start` and `end` timestamps. It would be pure guesswork.

2.  **Sending the entire `"words"` array:**
    *   **Pro:** Contains all the necessary data.
    *   **Con:** It's extremely inefficient. You'd be sending a huge amount of data (including `confidence`, `speaker`, etc.) that isn't relevant to the core task. This dramatically increases the token count (cost) and can easily exceed the LLM's context window for longer videos. It also asks the LLM to parse a complex JSON structure, which can lead to errors.

### The Solution: The "Timestamp-Enriched Transcript"

The best practice is a hybrid approach. We will pre-process the transcript data into a clean, human-readable format that embeds timing information directly into the text before sending it to <PERSON>.

**How it Works:**

Instead of sending raw JSON, we'll create a single string that looks like this:

**Before (Raw Text):**
`"Rp50.000 bisa bikin coklat sendiri di Jogja. Pertama, harus reservasi dulu di kontak personnya lewat Instagram. Ini itu tuh ada...."`

**After (Enriched Transcript):**
`"[0.8s] Rp50.000 bisa bikin coklat sendiri di Jogja. [2.5s] Pertama, harus reservasi dulu di kontak personnya lewat Instagram. [5.1s] Ini itu tuh ada...."`

This format is the best of both worlds:
*   **It's concise:** It dramatically reduces the token count compared to the full `words` JSON.
*   **It's context-rich:** Gemini can now see exactly when each phrase or sentence begins.
*   **It's LLM-friendly:** This format is easy for the model to understand and parse.

---

### **Step 1: Create a Pre-processing Function**

First, you'll need a utility function that converts the `words` array into this enriched string. You can place this in a utility file or directly in your `analyze` API route.

**Prompt for your AI Agent:**

> Create a TypeScript function named `generateEnrichedTranscript`. This function will take an array of `word` objects (each with `text`, `start`, and `end` in milliseconds) as input. It should group words together to form sentences or phrases, and then prepend the start time of the first word in each group to the text.
>
> **Requirements:**
> 1.  It should iterate through the `words` array.
> 2.  It should create segments of text. A new segment should begin every 10-15 words OR when a pause of more than 700ms is detected between words.
> 3.  For each new segment, it should prepend the start time in seconds (milliseconds / 1000) formatted as `[ss.s] `. For example, `808ms` becomes `[0.8s] `.
> 4.  The function should return a single, formatted string.

---

### **Step 2: The New and Improved Gemini Prompt**

Now, we can write a much more powerful and precise prompt for Gemini. This is a "zero-shot" prompt where we teach the AI exactly what to do.

**Prompt for your AI Agent:**

> Replace the existing Gemini API call in `src/app/api/analyze/route.ts` with a new implementation. Use the `generateEnrichedTranscript` function to pre-process the transcript. Then, use the following detailed prompt when calling the Gemini API.

```text
You are an expert viral video editor for platforms like TikTok, YouTube Shorts, and Instagram Reels. Your task is to analyze the following transcript and identify 3 to 5 of the most engaging, hook-worthy, and high-impact segments to be turned into short clips.

The transcript is formatted with timestamps like [ss.s] indicating the start time in seconds of the following text.

**RULES:**
1.  **Clip Duration:** Each clip you identify should be between 15 and 60 seconds long.
2.  **Coherency:** Ensure clips are self-contained and don't start or end abruptly in the middle of a sentence.
3.  **Selection Criteria:** Prioritize segments that contain:
    - A strong hook or opening question.
    - A key takeaway, lesson, or "Aha!" moment.
    - A surprising statement or a big reveal.
    - High emotional energy (excitement, humor, passion).
4.  **Timestamp Accuracy:** The 'start' and 'end' times you provide MUST be derived from the timestamps present in the text. You will need to estimate the end time based on the text that follows a start timestamp.
5.  **Output Format:** Your response MUST be a valid JSON array of objects and NOTHING else. Do not include any explanations, markdown formatting, or any text outside of the JSON array.

**EXAMPLE OUTPUT FORMAT:**
[
  {
    "start": 15.2,
    "end": 45.8,
    "description": "Explains the main surprising outcome, which makes a great hook."
  },
  {
    "start": 88.5,
    "end": 110.1,
    "description": "A funny and relatable anecdote that is highly shareable."
  }
]

**TRANSCRIPT TO ANALYZE:**
---
{enriched_transcript_string_goes_here}
---
```

This new prompt is vastly superior because:
*   **It teaches the AI the input format:** `The transcript is formatted with timestamps like [ss.s]...`
*   **It sets clear constraints:** Clip duration, coherency, and selection criteria.
*   **It demands accurate timing:** `...must be derived from the timestamps...`
*   **It enforces a strict output schema:** `Your response MUST be a valid JSON array...`

This solves the timing problem completely while keeping your token usage efficient and dramatically increasing the quality and reliability of the results.